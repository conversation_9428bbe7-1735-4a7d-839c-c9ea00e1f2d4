import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth-helpers';
import { createClient } from '@supabase/supabase-js';
import { google } from 'googleapis';

// Helper function to record test results
function recordTest(results: any, name: string, status: boolean, details = '') {
  results.total++;
  if (status) {
    results.passed++;
  } else {
    results.failed++;
  }
  results.tests.push({ name, status, details });
}

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication and super admin role
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { adminUser } = authResult;
    const isSuperAdmin = adminUser?.roles?.some((role: any) => role.name === 'super_admin');
    
    if (!isSuperAdmin) {
      return NextResponse.json(
        { error: 'Super admin access required' },
        { status: 403 }
      );
    }

    // Run comprehensive functionality tests
    const results = {
      total: 0,
      passed: 0,
      failed: 0,
      tests: [] as any[]
    };



    // Test Environment Variables
    const requiredVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY',
      'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',
      'NEXT_PUBLIC_CLOUDINARY_API_KEY',
      'CLOUDINARY_API_SECRET',
      'GOOGLE_CLIENT_EMAIL',
      'GOOGLE_PRIVATE_KEY'
    ];

    for (const varName of requiredVars) {
      const value = process.env[varName];
      recordTest(
        results,
        `Environment Variable: ${varName}`,
        !!value,
        value ? 'Set' : 'Missing'
      );
    }

    // Test Supabase Connection
    try {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );

      const { data, error } = await supabase.from('trips').select('count').limit(1);
      recordTest(results, 'Supabase Database Connection', !error, error?.message || 'Connected');

      // Test each table
      const tables = ['trips', 'blog_posts', 'trip_photos_details', 'team_members', 'inquiries', 'admin_profiles'];
      for (const table of tables) {
        try {
          const { data, error } = await supabase.from(table).select('*').limit(1);
          recordTest(results, `Database Table: ${table}`, !error, error?.message || 'Accessible');
        } catch (err: any) {
          recordTest(results, `Database Table: ${table}`, false, err.message);
        }
      }
    } catch (error: any) {
      recordTest(results, 'Supabase Setup', false, error.message);
    }

    // Test Google Drive API
    try {
      const credentials = {
        client_email: process.env.GOOGLE_CLIENT_EMAIL,
        private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      };

      if (credentials.client_email && credentials.private_key) {
        recordTest(results, 'Google Credentials', true, 'Present');

        const auth = new google.auth.JWT({
          email: credentials.client_email,
          key: credentials.private_key,
          scopes: ['https://www.googleapis.com/auth/drive'],
        });

        await auth.authorize();
        recordTest(results, 'Google Authentication', true, 'Successful');

        const drive = google.drive({ version: 'v3', auth });
        const response = await drive.about.get({ fields: 'user' });
        recordTest(results, 'Google Drive Access', true, `User: ${response.data.user?.emailAddress}`);
      } else {
        recordTest(results, 'Google Credentials', false, 'Missing credentials');
      }
    } catch (error: any) {
      recordTest(results, 'Google Drive API', false, error.message);
    }

    // Test Cloudinary API
    try {
      const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
      const apiKey = process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY;
      const apiSecret = process.env.CLOUDINARY_API_SECRET;

      recordTest(results, 'Cloudinary Credentials', !!(cloudName && apiKey && apiSecret),
        (cloudName && apiKey && apiSecret) ? 'All credentials present' : 'Missing credentials');

      if (cloudName && apiKey && apiSecret) {
        const testUrl = `https://api.cloudinary.com/v1_1/${cloudName}/resources/image`;
        const auth = Buffer.from(`${apiKey}:${apiSecret}`).toString('base64');

        const response = await fetch(testUrl, {
          headers: { 'Authorization': `Basic ${auth}` },
        });

        recordTest(results, 'Cloudinary API Access', response.ok, `Status: ${response.status}`);
      }
    } catch (error: any) {
      recordTest(results, 'Cloudinary API', false, error.message);
    }

    // Test API Endpoints
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    const endpoints = [
      { path: '/api/trips', method: 'GET' },
      { path: '/api/blog', method: 'GET' },
      { path: '/api/admin/dashboard', method: 'GET' },
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(`${baseUrl}${endpoint.path}`, {
          method: endpoint.method,
          headers: {
            'Cookie': request.headers.get('cookie') || '',
          },
        });

        const isSuccess = response.status < 500;
        recordTest(
          results,
          `API Endpoint: ${endpoint.method} ${endpoint.path}`,
          isSuccess,
          `Status: ${response.status}`
        );
      } catch (error: any) {
        recordTest(results, `API Endpoint: ${endpoint.method} ${endpoint.path}`, false, error.message);
      }
    }

    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: results.total,
        passed: results.passed,
        failed: results.failed,
        successRate: ((results.passed / results.total) * 100).toFixed(1)
      },
      tests: results.tests,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        baseUrl: baseUrl
      }
    };

    return NextResponse.json(report);

  } catch (error: any) {
    console.error('Functionality test error:', error);
    return NextResponse.json(
      { error: 'Failed to run functionality test', details: error.message },
      { status: 500 }
    );
  }
}
